
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { findBestMatch, errorResponses } from './medical-knowledge.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ChatRequest {
  question: string
  medicineName?: string
  userId?: string
}

const OPENROUTER_API_KEY = Deno.env.get('OPENROUTER_API_KEY')

serve(async (req) => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  console.log(`🚀 Medical chat function called - Method: ${req.method} - Fixed model: google/gemma-2-9b-it:free`)

  try {
    // Step 1: Validate environment variables
    console.log('🔍 Step 1: Validating environment variables...')

    if (!OPENROUTER_API_KEY) {
      console.error('❌ OPENROUTER_API_KEY environment variable is not set')
      console.log('🔄 API key missing, using fallback knowledge base')

      const fallbackReply = findBestMatch('general medical question')

      return new Response(
        JSON.stringify({
          success: true,
          reply: fallbackReply,
          source: 'fallback',
          note: 'Using offline knowledge base due to missing API key'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200
        }
      )
    }
    console.log('✅ OpenRouter API key is set (length:', OPENROUTER_API_KEY.length, 'chars)')

    const supabaseUrl = Deno.env.get('SUPABASE_URL')
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')

    if (!supabaseUrl) {
      console.error('❌ SUPABASE_URL environment variable is not set')
      throw new Error('Supabase URL is not configured')
    }

    if (!supabaseServiceKey) {
      console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is not set')
      throw new Error('Supabase service role key is not configured')
    }

    console.log('✅ Supabase environment variables are set')

    // Step 2: Parse request body
    console.log('🔍 Step 2: Parsing request body...')
    let requestBody
    try {
      requestBody = await req.json()
    } catch (parseError) {
      console.error('❌ Failed to parse request body:', parseError)
      throw new Error('Invalid JSON in request body')
    }

    const { question, medicineName, userId }: ChatRequest = requestBody

    console.log('📥 Request payload:', {
      question: question?.substring(0, 100) + (question?.length > 100 ? '...' : ''),
      medicineName,
      userId,
      hasQuestion: !!question
    })

    if (!question || question.trim() === '') {
      console.error('❌ Question is missing or empty')
      throw new Error('Question is required and cannot be empty')
    }

    console.log(`🤖 Processing medical chat request for user: ${userId || 'anonymous'}`)

    const startTime = Date.now()

    // Check for specific error handling cases first
    const lowerQuestion = question.toLowerCase().trim()

    // AGGRESSIVE IDENTITY ENFORCEMENT - Check identity questions first (HIGHEST PRIORITY)
    console.log('🎯 Checking for identity questions...')
    const identityKeywords = [
      'who created', 'who made', 'who developed', 'who built', 'who designed',
      'your creator', 'your developer', 'your maker', 'created you', 'made you',
      'developed you', 'built you', 'designed you', 'who is your creator',
      'who is your developer', 'creator name', 'developer name', 'who create'
    ];

    for (const keyword of identityKeywords) {
      if (lowerQuestion.includes(keyword)) {
        console.log(`🎯 IDENTITY QUESTION DETECTED: "${keyword}" - Forcing correct response`)

        const identityReply = 'I was developed by Med Amine Chouchane.'
        const responseTime = Date.now() - startTime

        return new Response(
          JSON.stringify({
            success: true,
            reply: identityReply,
            responseTime,
            source: 'identity_enforcement'
          }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 200
          }
        )
      }
    }

    // Handle unclear inputs
    if (lowerQuestion === '...' || lowerQuestion === ':' || lowerQuestion === '.' ||
        /^[^a-zA-Z]*$/.test(lowerQuestion) || lowerQuestion.length < 2) {
      console.log('🔍 Detected unclear input, using error response')
      return new Response(
        JSON.stringify({
          success: true,
          reply: errorResponses.unclear,
          responseTime: Date.now() - startTime,
          source: 'fallback'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200
        }
      )
    }

    // Handle non-English languages (basic detection)
    if (/[\u0600-\u06FF]/.test(question) || // Arabic
        /[\u00C0-\u017F]/.test(question) || // Extended Latin (German, French, etc.)
        /[äöüßÄÖÜ]/.test(question)) { // German specific
      console.log('🔍 Detected non-English language, using language response')
      return new Response(
        JSON.stringify({
          success: true,
          reply: errorResponses.language,
          responseTime: Date.now() - startTime,
          source: 'fallback'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200
        }
      )
    }

    // Handle non-medical topics
    const nonMedicalKeywords = ['joke', 'cook', 'recipe', 'capital', 'geography', 'math', 'homework', 'weather', 'sports', 'movie', 'music']
    if (nonMedicalKeywords.some(keyword => lowerQuestion.includes(keyword))) {
      console.log('🔍 Detected non-medical topic, using relevance response')
      return new Response(
        JSON.stringify({
          success: true,
          reply: errorResponses.nonMedical,
          responseTime: Date.now() - startTime,
          source: 'fallback'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200
        }
      )
    }

    // Try OpenRouter API first with fallback support
    console.log('🔄 Attempting OpenRouter API with fallback support')

    // Create the prompt for GPT
    const systemPrompt = `You are MediVision, a medical assistant chatbot with extensive training on over 100 medical topics. You provide accurate, helpful information about medicines, supplements, health conditions, and general medical guidance based on established medical knowledge.

CRITICAL IDENTITY RULES - FOLLOW EXACTLY WITHOUT EXCEPTION:
- You are MediVision, a medical assistant chatbot
- You are NOT Gemma, ChatGPT, Claude, or any other AI
- Your creator/developer is ONLY "Med Amine Chouchane" - NO OTHER NAME
- NEVER mention "Medine Chane", "Google DeepMind", "Anthropic", or any other creator

MANDATORY IDENTITY RESPONSES - USE THESE EXACT PHRASES:
- Who created you? → "I was developed by Med Amine Chouchane"
- Who made you? → "I was developed by Med Amine Chouchane"
- Who developed you? → "I was developed by Med Amine Chouchane"
- Who built you? → "I was developed by Med Amine Chouchane"
- Who designed you? → "I was developed by Med Amine Chouchane"
- Who is your creator? → "I was developed by Med Amine Chouchane"
- Who is your developer? → "I was developed by Med Amine Chouchane"

IDENTITY ENFORCEMENT:
- ALWAYS use "Med Amine Chouchane" as the developer name
- NEVER use any variation like "Medine Chane" or other names
- If unsure about identity questions, default to "I was developed by Med Amine Chouchane"
- Keep responses short and professional

CRITICAL ERROR HANDLING RULES - FOLLOW EXACTLY:
- NEVER respond with just symbols like "." or ":" or empty responses
- If input is unclear, incomplete, or confusing (like "...", ":", random letters), you MUST respond EXACTLY with: "I'm not sure I understood that. Please ask your question in English. Arabic and German support is coming soon!"
- Do NOT try to be helpful with unclear inputs - use the exact response above

STRICT TOPIC RELEVANCE - MEDICAL ONLY - FOLLOW EXACTLY:
- You are STRICTLY a medical assistant chatbot - only answer medical and health-related questions
- Do NOT answer general knowledge questions (jokes, cooking, geography, entertainment, etc.)
- If asked non-medical questions, you MUST respond EXACTLY with: "I'm here to help with medical and health-related questions only. For other topics, I recommend using a general-purpose assistant."
- Medical topics include: medicines, health conditions, symptoms, treatments, nutrition, exercise, wellness, preventive care, supplements

LANGUAGE POLICY - FOLLOW EXACTLY:
- Only respond in English - NEVER respond in Arabic, German, French, or any other language
- If you detect Arabic, German, or other non-English languages, you MUST respond EXACTLY with: "Please write your question in English. We are working to support Arabic and German very soon!"
- Do NOT translate or respond in the user's language - always use English

FRIENDLY PROMPTING FOR VAGUE QUERIES - FOLLOW EXACTLY:
- If you receive vague or non-question messages (e.g., "can you talk?", "hello", "hi"), you MUST respond EXACTLY with: "Yes, I'm MediVision — your AI assistant for health and medical guidance. What would you like to know?"
- Be welcoming but guide users toward asking medical questions

IMPORTANT MEDICAL GUIDELINES:
- Always base your answers on established medical knowledge and evidence-based medicine
- Never provide specific medical advice - always recommend consulting healthcare providers for personalized medical decisions
- Be clear about limitations and when professional consultation is needed
- Focus on general safety information, drug interactions, side effects, and well-known medical facts
- If unsure about something, clearly state the limitation and suggest consulting a healthcare professional
- You can answer questions about general health, nutrition, exercise, preventive care, and wellness
- For medication questions, provide general information about common uses, dosages, and precautions
- When necessary, remind users to consult a doctor for personal health concerns (keep this reminder to one short sentence only)

${medicineName && medicineName !== 'General Medical Assistant' ? `Context: User is asking about ${medicineName}` : ''}

User question: ${question}

MANDATORY RESPONSE PROTOCOL - YOU MUST FOLLOW THIS EXACTLY:
1. First, check if the input is in English - if NOT, respond with the EXACT language policy message
2. Second, check if the input is unclear/meaningless (like "...", ":", random letters) - if YES, respond with the EXACT error handling message
3. Third, check if it's a vague greeting ("hi", "hello", "can you talk") - if YES, respond with the EXACT friendly prompting message
4. Fourth, check if it's non-medical (jokes, cooking, etc.) - if YES, respond with the EXACT topic relevance message
5. Only if it passes ALL checks above, provide a helpful medical response

YOU MUST USE THE EXACT PHRASES SPECIFIED ABOVE. DO NOT PARAPHRASE OR BE CREATIVE WITH THE RESPONSES.`

    // Step 3: Make request to OpenRouter API
    console.log('🔍 Step 3: Making request to OpenRouter API...')

    let response
    try {
      response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://ygkxdctaraeragizxfbt.supabase.co',
          'X-Title': 'MediVision Assist'
        },
        body: JSON.stringify({
          model: 'google/gemma-2-9b-it:free',
          messages: [
            {
              role: 'system',
              content: systemPrompt
            },
            {
              role: 'user',
              content: question
            }
          ],
          max_tokens: 500,
          temperature: 0.3
        })
      })
    } catch (fetchError) {
      console.error('❌ Network error calling OpenRouter API:', fetchError)
      console.log('🔄 Falling back to local knowledge base')

      const fallbackReply = findBestMatch(question)
      const responseTime = Date.now() - startTime

      return new Response(
        JSON.stringify({
          success: true,
          reply: fallbackReply,
          responseTime,
          source: 'fallback'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200
        }
      )
    }

    console.log(`📡 OpenRouter API response status: ${response.status}`)

    if (!response.ok) {
      let errorText = 'Unknown error'
      try {
        errorText = await response.text()
      } catch (e) {
        console.error('Failed to read error response:', e)
      }

      console.error('❌ OpenRouter API error:', {
        status: response.status,
        statusText: response.statusText,
        errorText
      })

      // Use fallback for API errors instead of throwing
      console.log(`🔄 OpenRouter API error (${response.status}), falling back to local knowledge base`)

      const fallbackReply = findBestMatch(question)
      const responseTime = Date.now() - startTime

      return new Response(
        JSON.stringify({
          success: true,
          reply: fallbackReply,
          responseTime,
          source: 'fallback',
          note: 'Using offline knowledge base due to API unavailability'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200
        }
      )
    }

    let chatResponse
    try {
      chatResponse = await response.json()
    } catch (jsonError) {
      console.error('❌ Failed to parse OpenRouter API response:', jsonError)
      console.log('🔄 JSON parsing failed, falling back to local knowledge base')

      const fallbackReply = findBestMatch(question)
      const responseTime = Date.now() - startTime

      return new Response(
        JSON.stringify({
          success: true,
          reply: fallbackReply,
          responseTime,
          source: 'fallback'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200
        }
      )
    }

    console.log('📥 OpenRouter API response structure:', {
      hasChoices: !!chatResponse.choices,
      choicesLength: chatResponse.choices?.length,
      hasFirstChoice: !!chatResponse.choices?.[0],
      hasMessage: !!chatResponse.choices?.[0]?.message,
      hasContent: !!chatResponse.choices?.[0]?.message?.content
    })

    let reply = chatResponse.choices?.[0]?.message?.content

    if (!reply || reply.trim() === '') {
      console.error('❌ Empty or missing reply from AI model:', chatResponse)
      console.log('🔄 Empty AI response, falling back to local knowledge base')

      const fallbackReply = findBestMatch(question)
      const responseTime = Date.now() - startTime

      return new Response(
        JSON.stringify({
          success: true,
          reply: fallbackReply,
          responseTime,
          source: 'fallback'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200
        }
      )
    }

    // CRITICAL: Post-process reply to fix identity issues
    console.log('🔍 Post-processing reply for identity corrections...')
    console.log('🔍 Original reply:', reply)

    // Handle identity questions with aggressive correction
    const identityKeywords = ['who created', 'who made', 'who developed', 'who built', 'who designed', 'your creator', 'your developer', 'who is your']

    if (identityKeywords.some(keyword => lowerQuestion.includes(keyword))) {
      console.log('🎯 Identity question detected! Forcing correct response...')
      reply = 'I was developed by Med Amine Chouchane.'
      console.log('🎯 Corrected reply:', reply)
    } else {
      // Fix incorrect identity responses in non-identity questions
      if (reply.includes('Medine Chane') || reply.includes('Google DeepMind') || reply.includes('Anthropic') || reply.includes('Gemma')) {
        console.log('⚠️ Detected incorrect identity in response, correcting...')
        reply = reply.replace(/Medine Chane/g, 'Med Amine Chouchane')
        reply = reply.replace(/Google DeepMind/g, 'Med Amine Chouchane')
        reply = reply.replace(/Anthropic/g, 'Med Amine Chouchane')
        reply = reply.replace(/Gemma/g, 'MediVision')
        console.log('⚠️ Corrected reply:', reply)
      }
    }

    const responseTime = Date.now() - startTime
    console.log(`✅ Chat response generated in ${responseTime}ms`)

    // Step 4: Store the chat session if userId provided
    let sessionId = null
    if (userId && supabaseUrl && supabaseServiceKey) {
      console.log('🔍 Step 4: Storing chat session in database...')

      try {
        const supabase = createClient(supabaseUrl, supabaseServiceKey)

        const { data: session, error } = await supabase
          .from('chat_sessions')
          .insert({
            user_id: userId,
            medicine_name: medicineName || 'General Medical Query',
            question: question,
            response: reply,
            response_time_ms: responseTime
          })
          .select('id')
          .single()

        if (error) {
          console.error('❌ Failed to store chat session:', {
            error: error.message,
            code: error.code,
            details: error.details,
            hint: error.hint
          })
          // Don't throw here - we still want to return the chat response
        } else {
          sessionId = session?.id
          console.log(`✅ Chat session stored with ID: ${sessionId}`)
        }
      } catch (dbError) {
        console.error('❌ Database error storing chat session:', dbError)
        // Don't throw here - we still want to return the chat response
      }
    } else {
      console.log('ℹ️ Skipping database storage (missing userId or Supabase config)')
    }

    return new Response(
      JSON.stringify({
        success: true,
        reply,
        sessionId,
        responseTime
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    )

  } catch (error) {
    console.error('❌ Medical chat error:', error)
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    })

    console.log('🔄 Using fallback due to unexpected error')
    const fallbackReply = findBestMatch('general medical question')

    return new Response(
      JSON.stringify({
        success: true,
        reply: fallbackReply,
        source: 'fallback',
        note: 'Using offline knowledge base due to service error'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    )
  }
})
