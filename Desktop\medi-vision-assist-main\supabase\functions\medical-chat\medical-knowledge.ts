/**
 * Medical Knowledge Base for MediVision Chatbot
 * Contains 100+ medical questions and responses for offline fallback
 */

export interface MedicalKnowledge {
  question: string;
  response: string;
  keywords: string[];
  category: string;
}

export const medicalKnowledgeBase: MedicalKnowledge[] = [
  // Pain Relief Medications
  {
    question: "What is aspirin used for?",
    response: "I'm MediV<PERSON>, your medical assistant. Aspirin is commonly used for pain relief, fever reduction, and inflammation. It's also used in low doses for heart attack and stroke prevention. Always consult your doctor for proper dosage.",
    keywords: ["aspirin", "pain", "fever", "inflammation", "heart"],
    category: "pain_relief"
  },
  {
    question: "What are the side effects of ibuprofen?",
    response: "Ibuprofen side effects may include stomach upset, heartburn, dizziness, and in rare cases, kidney problems or allergic reactions. Take with food to reduce stomach irritation. Consult a healthcare provider if you experience severe side effects.",
    keywords: ["ibuprofen", "side effects", "stomach", "kidney", "allergic"],
    category: "pain_relief"
  },
  {
    question: "Can I take paracetamol with other medications?",
    response: "Paracetamol can interact with certain medications like warfarin and some antibiotics. Always check with your pharmacist or doctor before combining medications. Don't exceed the recommended dose to avoid liver damage.",
    keywords: ["paracetamol", "acetaminophen", "interactions", "warfarin", "liver"],
    category: "pain_relief"
  },

  // Antibiotics
  {
    question: "What is amoxicillin used for?",
    response: "Amoxicillin is an antibiotic used to treat bacterial infections like pneumonia, bronchitis, ear infections, and urinary tract infections. Complete the full course even if you feel better. Consult your doctor for proper diagnosis.",
    keywords: ["amoxicillin", "antibiotic", "bacterial", "infection", "pneumonia"],
    category: "antibiotics"
  },
  {
    question: "Should I take probiotics with antibiotics?",
    response: "Yes, probiotics can help maintain healthy gut bacteria during antibiotic treatment. Take them 2-3 hours apart from your antibiotic dose. Consult your healthcare provider for the best probiotic recommendation.",
    keywords: ["probiotics", "antibiotics", "gut", "bacteria", "healthy"],
    category: "antibiotics"
  },

  // Heart Health
  {
    question: "How can I prevent heart disease?",
    response: "Heart disease prevention includes regular exercise, a healthy diet low in saturated fats, not smoking, managing stress, and controlling blood pressure and cholesterol. Regular check-ups with your doctor are essential.",
    keywords: ["heart disease", "prevention", "exercise", "diet", "blood pressure"],
    category: "heart_health"
  },
  {
    question: "What are the symptoms of a heart attack?",
    response: "Heart attack symptoms include chest pain, shortness of breath, nausea, sweating, and pain in arms, neck, or jaw. If you suspect a heart attack, call emergency services immediately. Don't delay seeking medical help.",
    keywords: ["heart attack", "chest pain", "emergency", "symptoms", "shortness"],
    category: "heart_health"
  },

  // Diabetes
  {
    question: "What is diabetes?",
    response: "Diabetes is a condition where blood sugar levels are too high. Type 1 requires insulin injections, while Type 2 can often be managed with diet, exercise, and medication. Regular monitoring and medical care are essential.",
    keywords: ["diabetes", "blood sugar", "insulin", "type 1", "type 2"],
    category: "diabetes"
  },
  {
    question: "How do I manage diabetes?",
    response: "Diabetes management includes monitoring blood sugar, taking prescribed medications, eating a balanced diet, regular exercise, and routine medical check-ups. Work closely with your healthcare team for the best results.",
    keywords: ["diabetes", "management", "blood sugar", "diet", "exercise"],
    category: "diabetes"
  },

  // Mental Health
  {
    question: "What are signs of depression?",
    response: "Depression signs include persistent sadness, loss of interest in activities, fatigue, sleep changes, and difficulty concentrating. If you're experiencing these symptoms, please reach out to a mental health professional.",
    keywords: ["depression", "sadness", "fatigue", "sleep", "mental health"],
    category: "mental_health"
  },
  {
    question: "How can I manage anxiety?",
    response: "Anxiety management includes deep breathing exercises, regular physical activity, adequate sleep, limiting caffeine, and stress management techniques. Consider speaking with a counselor or therapist for additional support.",
    keywords: ["anxiety", "breathing", "exercise", "sleep", "stress"],
    category: "mental_health"
  },

  // Vitamins and Supplements
  {
    question: "Do I need vitamin D supplements?",
    response: "Vitamin D is important for bone health and immune function. Many people have low levels, especially in winter. A blood test can determine if you need supplements. Consult your doctor for proper dosage.",
    keywords: ["vitamin d", "supplements", "bone health", "immune", "blood test"],
    category: "vitamins"
  },
  {
    question: "What are the benefits of vitamin C?",
    response: "Vitamin C supports immune function, helps with iron absorption, and acts as an antioxidant. Most people get enough from fruits and vegetables. Supplements may help during illness, but consult your healthcare provider.",
    keywords: ["vitamin c", "immune", "antioxidant", "iron", "fruits"],
    category: "vitamins"
  },

  // General Health
  {
    question: "How much water should I drink daily?",
    response: "Most adults should drink about 8 glasses (64 ounces) of water daily, but needs vary based on activity, climate, and health conditions. Listen to your body and increase intake during exercise or hot weather.",
    keywords: ["water", "hydration", "daily", "exercise", "health"],
    category: "general_health"
  },
  {
    question: "How much sleep do I need?",
    response: "Most adults need 7-9 hours of sleep per night. Good sleep hygiene includes a regular schedule, comfortable environment, and avoiding screens before bedtime. Consult a doctor if you have persistent sleep problems.",
    keywords: ["sleep", "hours", "hygiene", "schedule", "bedtime"],
    category: "general_health"
  },

  // Common Conditions
  {
    question: "How do I treat a common cold?",
    response: "Cold treatment includes rest, fluids, throat lozenges, and over-the-counter pain relievers. Most colds resolve in 7-10 days. See a doctor if symptoms worsen or persist beyond two weeks.",
    keywords: ["cold", "rest", "fluids", "throat", "symptoms"],
    category: "common_conditions"
  },
  {
    question: "What helps with headaches?",
    response: "Headache relief may include rest in a dark room, hydration, over-the-counter pain relievers, and stress management. If headaches are frequent or severe, consult a healthcare provider for proper evaluation.",
    keywords: ["headache", "rest", "dark room", "hydration", "stress"],
    category: "common_conditions"
  },

  // Identity and Bot Information - Comprehensive Dataset
  {
    question: "Who created you?",
    response: "I was developed by Med Amine Chouchane.",
    keywords: ["created", "made", "developed", "built", "creator", "developer", "who", "create"],
    category: "identity"
  },
  {
    question: "Who made you?",
    response: "I was developed by Med Amine Chouchane.",
    keywords: ["made", "created", "developed", "built", "maker", "creator", "who", "make"],
    category: "identity"
  },
  {
    question: "Who developed you?",
    response: "I was developed by Med Amine Chouchane.",
    keywords: ["developed", "created", "made", "built", "developer", "creator", "who", "develop"],
    category: "identity"
  },
  {
    question: "Who built you?",
    response: "I was developed by Med Amine Chouchane.",
    keywords: ["built", "created", "made", "developed", "builder", "creator", "who", "build"],
    category: "identity"
  },
  {
    question: "Who designed you?",
    response: "I was developed by Med Amine Chouchane.",
    keywords: ["designed", "created", "made", "developed", "designer", "creator", "who", "design"],
    category: "identity"
  },
  {
    question: "Who is your creator?",
    response: "I was developed by Med Amine Chouchane.",
    keywords: ["creator", "developer", "maker", "your", "who", "is"],
    category: "identity"
  },
  {
    question: "Who is your developer?",
    response: "I was developed by Med Amine Chouchane.",
    keywords: ["developer", "creator", "maker", "your", "who", "is"],
    category: "identity"
  },
  {
    question: "What is your creator name?",
    response: "I was developed by Med Amine Chouchane.",
    keywords: ["creator", "name", "developer", "what", "your"],
    category: "identity"
  },
  {
    question: "Who are you?",
    response: "I'm MediVision, your medical assistant chatbot developed by Med Amine Chouchane. I help with medical questions and medicine identification.",
    keywords: ["who", "are", "you", "identity", "name"],
    category: "identity"
  },
  {
    question: "What is your name?",
    response: "My name is MediVision. I'm your medical assistant chatbot developed by Med Amine Chouchane.",
    keywords: ["name", "called", "identity", "what", "your"],
    category: "identity"
  },
  {
    question: "Tell me about yourself",
    response: "I'm MediVision, your medical assistant chatbot developed by Med Amine Chouchane. I help with medical questions and medicine identification.",
    keywords: ["tell", "about", "yourself", "identity", "who"],
    category: "identity"
  },

  // Enhanced Natural Conversation Dataset - 30+ Q&A Pairs
  // Confirmation-style questions
  {
    question: "Did Med Amine create you?",
    response: "Yes, Med Amine Chouchane created me. I'm MediVision, your medical assistant.",
    keywords: ["did", "med", "amine", "create", "you", "yes", "confirm"],
    category: "identity"
  },
  {
    question: "So Med Amine made you?",
    response: "Yes, that's correct! Med Amine Chouchane developed me to help with medical questions.",
    keywords: ["so", "med", "amine", "made", "you", "correct", "confirm"],
    category: "identity"
  },
  {
    question: "Are you made by Med Amine?",
    response: "Yes, I was developed by Med Amine Chouchane.",
    keywords: ["are", "you", "made", "by", "med", "amine", "confirm"],
    category: "identity"
  },
  {
    question: "Is Med Amine your creator?",
    response: "Yes, Med Amine Chouchane is my creator and developer.",
    keywords: ["is", "med", "amine", "your", "creator", "confirm"],
    category: "identity"
  },
  {
    question: "Did Med Amine Chouchane develop you?",
    response: "Yes, exactly! Med Amine Chouchane developed me as your medical assistant.",
    keywords: ["did", "med", "amine", "chouchane", "develop", "you", "confirm"],
    category: "identity"
  },

  // Casual greetings and small talk
  {
    question: "How are you?",
    response: "I'm functioning well, thank you for asking! How can I assist you with your health questions today?",
    keywords: ["how", "are", "you", "greeting", "well"],
    category: "greeting"
  },
  {
    question: "How are you doing?",
    response: "I'm doing great, thanks! Ready to help with any medical questions you might have.",
    keywords: ["how", "are", "you", "doing", "greeting", "great"],
    category: "greeting"
  },
  {
    question: "How's it going?",
    response: "Everything's going well! I'm here and ready to help with your medical concerns. What can I assist you with?",
    keywords: ["how", "it", "going", "greeting", "well"],
    category: "greeting"
  },
  {
    question: "What's up?",
    response: "Hello! I'm here to help with medical questions and health guidance. What would you like to know?",
    keywords: ["what", "up", "greeting", "hello"],
    category: "greeting"
  },
  {
    question: "Good morning",
    response: "Good morning! I'm MediVision, ready to assist you with medical information. How can I help you today?",
    keywords: ["good", "morning", "greeting"],
    category: "greeting"
  },
  {
    question: "Good afternoon",
    response: "Good afternoon! I'm here to help with any medical questions or health concerns you might have.",
    keywords: ["good", "afternoon", "greeting"],
    category: "greeting"
  },
  {
    question: "Good evening",
    response: "Good evening! I'm MediVision, your medical assistant. What health questions can I help you with tonight?",
    keywords: ["good", "evening", "greeting"],
    category: "greeting"
  },

  // Paraphrased identity questions
  {
    question: "Who's your maker?",
    response: "My maker is Med Amine Chouchane. I'm MediVision, designed to help with medical guidance.",
    keywords: ["who", "your", "maker", "identity"],
    category: "identity"
  },
  {
    question: "Who's behind this bot?",
    response: "Med Amine Chouchane is behind my development. I'm MediVision, your medical assistant.",
    keywords: ["who", "behind", "this", "bot", "identity"],
    category: "identity"
  },
  {
    question: "Who programmed you?",
    response: "I was programmed by Med Amine Chouchane to assist with medical questions and health guidance.",
    keywords: ["who", "programmed", "you", "identity"],
    category: "identity"
  },
  {
    question: "Who's your author?",
    response: "Med Amine Chouchane is my author and developer.",
    keywords: ["who", "your", "author", "identity"],
    category: "identity"
  },
  {
    question: "Who invented you?",
    response: "I was invented by Med Amine Chouchane as a medical assistant chatbot.",
    keywords: ["who", "invented", "you", "identity"],
    category: "identity"
  },

  // Conversational variations
  {
    question: "Can you tell me who made you?",
    response: "Of course! I was made by Med Amine Chouchane. I'm MediVision, here to help with your medical questions.",
    keywords: ["can", "you", "tell", "me", "who", "made", "you", "identity"],
    category: "identity"
  },
  {
    question: "I want to know who created you",
    response: "I'd be happy to tell you! Med Amine Chouchane created me to provide medical assistance and health guidance.",
    keywords: ["want", "to", "know", "who", "created", "you", "identity"],
    category: "identity"
  },
  {
    question: "Could you tell me about your creator?",
    response: "Certainly! My creator is Med Amine Chouchane, who developed me as a medical assistant to help with health questions.",
    keywords: ["could", "you", "tell", "me", "about", "your", "creator", "identity"],
    category: "identity"
  },

  // Friendly conversation starters
  {
    question: "Nice to meet you",
    response: "Nice to meet you too! I'm MediVision, your medical assistant developed by Med Amine Chouchane. How can I help you today?",
    keywords: ["nice", "to", "meet", "you", "greeting"],
    category: "greeting"
  },
  {
    question: "Pleasure to meet you",
    response: "The pleasure is mine! I'm MediVision, ready to assist with your medical questions. What would you like to know?",
    keywords: ["pleasure", "to", "meet", "you", "greeting"],
    category: "greeting"
  },
  {
    question: "Thanks for being here",
    response: "You're very welcome! I'm always here to help with medical questions and health guidance. What can I assist you with?",
    keywords: ["thanks", "for", "being", "here", "greeting"],
    category: "greeting"
  },

  // Identity clarifications
  {
    question: "Are you really made by Med Amine?",
    response: "Yes, absolutely! I was developed by Med Amine Chouchane specifically to provide medical assistance and health guidance.",
    keywords: ["are", "you", "really", "made", "by", "med", "amine", "confirm"],
    category: "identity"
  },
  {
    question: "Is it true that Med Amine created you?",
    response: "Yes, that's completely true! Med Amine Chouchane created me as your medical assistant chatbot.",
    keywords: ["is", "it", "true", "that", "med", "amine", "created", "you", "confirm"],
    category: "identity"
  },
  {
    question: "Med Amine is your developer, right?",
    response: "That's right! Med Amine Chouchane is my developer. I'm MediVision, here to help with your health questions.",
    keywords: ["med", "amine", "is", "your", "developer", "right", "confirm"],
    category: "identity"
  },

  // Casual identity questions
  {
    question: "Who's your boss?",
    response: "Med Amine Chouchane is my creator and developer. I'm MediVision, designed to assist with medical questions.",
    keywords: ["who", "your", "boss", "identity"],
    category: "identity"
  },
  {
    question: "Who do you work for?",
    response: "I work for you! I was created by Med Amine Chouchane to provide medical assistance and health guidance.",
    keywords: ["who", "do", "you", "work", "for", "identity"],
    category: "identity"
  },

  // Appreciation and thanks
  {
    question: "Thank you",
    response: "You're very welcome! I'm happy to help with any medical questions you might have. Feel free to ask me anything about health or medicines.",
    keywords: ["thank", "you", "thanks", "appreciation"],
    category: "greeting"
  },
  {
    question: "Thanks a lot",
    response: "You're most welcome! I'm here whenever you need medical guidance or have health-related questions.",
    keywords: ["thanks", "a", "lot", "appreciation"],
    category: "greeting"
  },
  {
    question: "I appreciate your help",
    response: "I'm glad I could help! Remember, I'm always here for medical questions, but please consult healthcare professionals for personalized medical advice.",
    keywords: ["appreciate", "your", "help", "appreciation"],
    category: "greeting"
  },

  // Capability questions with identity
  {
    question: "What can you do?",
    response: "I'm MediVision, developed by Med Amine Chouchane. I can help with medical questions, medicine information, symptoms guidance, and general health advice. What would you like to know?",
    keywords: ["what", "can", "you", "do", "capability"],
    category: "identity"
  },
  {
    question: "What are your abilities?",
    response: "As MediVision, created by Med Amine Chouchane, I can assist with medical questions, provide medicine information, help with symptom guidance, and offer general health advice. How can I help you?",
    keywords: ["what", "are", "your", "abilities", "capability"],
    category: "identity"
  }
];

// Greeting responses
export const greetingResponses = [
  "Yes, I'm MediVision — your AI assistant for health and medical guidance. What would you like to know?",
  "Hello! I'm MediVision, your medical assistant chatbot. How can I help you with your health questions today?",
  "Hi there! I'm MediVision, ready to assist you with medical information and health guidance. What can I help you with?"
];

// Error responses
export const errorResponses = {
  unclear: "I'm not sure I understood that. Please ask your question in English. Arabic and German support is coming soon!",
  nonMedical: "I'm here to help with medical and health-related questions only. For other topics, I recommend using a general-purpose assistant.",
  language: "Please write your question in English. We are working to support Arabic and German very soon!"
};

/**
 * Find the best matching response from the knowledge base
 */
export function findBestMatch(question: string): string {
  const lowerQuestion = question.toLowerCase().trim();

  console.log(`🔍 FindBestMatch called with: "${question}"`);

  // PRIORITY 1: Check for identity questions first (highest priority)
  const identityKeywords = [
    'who created', 'who made', 'who developed', 'who built', 'who designed',
    'your creator', 'your developer', 'your maker', 'created you', 'made you',
    'developed you', 'built you', 'designed you', 'who is your creator',
    'who is your developer', 'creator name', 'developer name'
  ];

  for (const keyword of identityKeywords) {
    if (lowerQuestion.includes(keyword)) {
      console.log(`🎯 Identity keyword detected: "${keyword}"`);
      return "I was developed by Med Amine Chouchane.";
    }
  }

  // PRIORITY 2: Check for greetings
  const greetingKeywords = ['hi', 'hello', 'hey', 'greetings', 'can you talk', 'are you there'];
  if (greetingKeywords.some(keyword => lowerQuestion.includes(keyword))) {
    console.log(`👋 Greeting detected`);
    return greetingResponses[Math.floor(Math.random() * greetingResponses.length)];
  }

  // PRIORITY 3: Check for "who are you" type questions
  if (lowerQuestion.includes('who are you') || lowerQuestion.includes('what are you')) {
    console.log(`🤖 "Who are you" question detected`);
    return "I'm MediVision, your medical assistant chatbot developed by Med Amine Chouchane. I help with medical questions and medicine identification.";
  }

  // PRIORITY 4: Check for name questions
  if (lowerQuestion.includes('your name') || lowerQuestion.includes('what is your name')) {
    console.log(`📛 Name question detected`);
    return "My name is MediVision. I'm your medical assistant chatbot developed by Med Amine Chouchane.";
  }

  // PRIORITY 5: Find best match in knowledge base using improved scoring
  let bestMatch: MedicalKnowledge | null = null;
  let highestScore = 0;

  console.log(`🔍 Searching ${medicalKnowledgeBase.length} knowledge base entries...`);

  for (const item of medicalKnowledgeBase) {
    let score = 0;

    // Higher weight for identity category
    if (item.category === 'identity') {
      score += 5; // Boost identity responses
    }

    // Check keywords with higher weight
    for (const keyword of item.keywords) {
      if (lowerQuestion.includes(keyword.toLowerCase())) {
        score += 3; // Increased from 2
      }
    }

    // Check question similarity
    const questionWords = item.question.toLowerCase().split(' ');
    for (const word of questionWords) {
      if (lowerQuestion.includes(word) && word.length > 2) { // Reduced from 3
        score += 1;
      }
    }

    if (score > highestScore) {
      highestScore = score;
      bestMatch = item;
    }
  }

  console.log(`📊 Best match score: ${highestScore}, threshold: 2`);

  if (bestMatch && highestScore >= 2) {
    console.log(`✅ Knowledge base match: "${bestMatch.question}"`);
    return bestMatch.response;
  }

  // Default medical response
  console.log(`❌ No match found, using default response`);
  return "I'm MediVision, your medical assistant. I can help with questions about medicines, health conditions, symptoms, and general wellness. Please ask me a specific medical question, and I'll do my best to help. Remember to consult healthcare professionals for personalized medical advice.";
}
