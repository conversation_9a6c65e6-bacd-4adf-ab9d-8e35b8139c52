import React, { useState, useEffect } from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { X, AlertTriangle, Beaker } from 'lucide-react';

const BetaNotification = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Check if user has already dismissed the notification
    const dismissed = localStorage.getItem('beta-notification-dismissed');
    if (!dismissed) {
      setIsVisible(true);
    }
  }, []);

  const handleDismiss = () => {
    setIsVisible(false);
    // Remember that user dismissed the notification
    localStorage.setItem('beta-notification-dismissed', 'true');
  };

  if (!isVisible) return null;

  return (
    <div className="fixed top-0 left-0 right-0 z-50 p-4">
      <div className="container mx-auto max-w-4xl">
        <Alert className="border-orange-200 bg-orange-50 text-orange-800 shadow-lg">
          <Beaker className="h-4 w-4 text-orange-600" />
          <div className="flex items-start justify-between w-full">
            <div className="flex-1">
              <AlertTitle className="text-orange-900 font-semibold mb-1">
                🧪 Beta Version - Development Mode
              </AlertTitle>
              <AlertDescription className="text-orange-700">
                <strong>MediVision</strong> is currently in beta testing. Some features may be experimental or under development. 
                Please report any issues you encounter. This is not a substitute for professional medical advice.
              </AlertDescription>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDismiss}
              className="text-orange-600 hover:text-orange-800 hover:bg-orange-100 ml-4 flex-shrink-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </Alert>
      </div>
    </div>
  );
};

export default BetaNotification;
