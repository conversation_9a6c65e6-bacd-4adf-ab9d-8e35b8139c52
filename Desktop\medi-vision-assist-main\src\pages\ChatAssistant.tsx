
import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { 
  Send, 
  Bot, 
  User, 
  ThumbsUp, 
  ThumbsDown, 
  Loader2,
  Stethoscope,
  AlertTriangle
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import Navigation from '@/components/Navigation';

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  sessionId?: string;
  medicineName?: string;
  feedbackGiven?: boolean;
}

const ChatAssistant = () => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'assistant',
      content: 'Hello! I\'m your AI medical assistant. I can help answer questions about medicines and supplements. Please note that my responses are for informational purposes only and should not replace professional medical advice. What would you like to know?',
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [medicineName, setMedicineName] = useState('General Medical Assistant');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // CLIENT-SIDE IDENTITY FIX - Detect and handle identity questions immediately
  const detectIdentityQuestion = (question: string): string | null => {
    const lowerQuestion = question.toLowerCase().trim();

    console.log('🎯 Client-side identity detection for:', question);

    // Identity keywords that should trigger correct response
    const identityKeywords = [
      'who created', 'who made', 'who developed', 'who built', 'who designed',
      'your creator', 'your developer', 'your maker', 'created you', 'made you',
      'developed you', 'built you', 'designed you', 'who is your creator',
      'who is your developer', 'creator name', 'developer name', 'who create'
    ];

    // Check for identity questions
    for (const keyword of identityKeywords) {
      if (lowerQuestion.includes(keyword)) {
        console.log(`🎯 IDENTITY QUESTION DETECTED: "${keyword}" - Using client-side override`);
        return 'I was developed by Med Amine Chouchane.';
      }
    }

    // Check for "who are you" type questions
    if (lowerQuestion.includes('who are you') || lowerQuestion.includes('what are you')) {
      console.log('🤖 "Who are you" question detected - Using client-side override');
      return "I'm MediVision, your medical assistant chatbot developed by Med Amine Chouchane. I help with medical questions and medicine identification.";
    }

    // Check for name questions
    if (lowerQuestion.includes('your name') || lowerQuestion.includes('what is your name')) {
      console.log('📛 Name question detected - Using client-side override');
      return "My name is MediVision. I'm your medical assistant chatbot developed by Med Amine Chouchane.";
    }

    return null; // Not an identity question, proceed with API call
  };

  const sendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date(),
      medicineName: medicineName
    };

    setMessages(prev => [...prev, userMessage]);
    const originalMessage = inputMessage; // Store original message before clearing
    setInputMessage('');
    setIsLoading(true);

    try {
      // CLIENT-SIDE IDENTITY FIX - Check for identity questions first
      const identityResponse = detectIdentityQuestion(originalMessage);

      if (identityResponse) {
        console.log('✅ Using client-side identity response');

        const assistantMessage: Message = {
          id: (Date.now() + 1).toString(),
          type: 'assistant',
          content: identityResponse,
          timestamp: new Date(),
          medicineName: medicineName
        };

        setMessages(prev => [...prev, assistantMessage]);
        setIsLoading(false);
        return; // Skip API call for identity questions
      }

      console.log('🚀 Sending chat request:', {
        question: originalMessage,
        medicineName,
        userId: user?.id
      });

      const response = await supabase.functions.invoke('medical-chat', {
        body: {
          question: originalMessage,
          medicineName: medicineName,
          userId: user?.id
        }
      });

      console.log('📥 Chat response received:', response);

      if (response.error) {
        console.error('❌ Function invocation error:', response.error);
        throw new Error(response.error.message || 'Function invocation failed');
      }

      if (!response.data) {
        console.error('❌ No data in response:', response);
        throw new Error('No data received from chat function');
      }

      if (!response.data.success) {
        console.error('❌ Chat function returned error:', response.data);
        throw new Error(response.data.error || 'Chat function failed');
      }

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: response.data.reply,
        timestamp: new Date(),
        sessionId: response.data.sessionId,
        medicineName: medicineName
      };

      setMessages(prev => [...prev, assistantMessage]);
      console.log('✅ Chat message added successfully');

    } catch (error) {
      console.error('❌ Chat error details:', {
        error,
        message: error.message,
        stack: error.stack,
        name: error.name
      });

      // Determine error type and provide appropriate message
      let errorContent = 'I apologize, but I\'m having trouble processing your request right now. Please try again later or consult with a healthcare professional for medical questions.';
      let toastDescription = 'Failed to get AI response. Please try again.';

      if (error.message?.includes('configuration') || error.message?.includes('not configured')) {
        errorContent = 'The AI service is currently unavailable due to configuration issues. Please contact support.';
        toastDescription = 'Service configuration error. Please contact support.';
      } else if (error.message?.includes('network') || error.message?.includes('fetch')) {
        errorContent = 'Network connection issue. Please check your internet connection and try again.';
        toastDescription = 'Network error. Please check your connection.';
      }

      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: errorContent,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);

      toast({
        title: "Error",
        description: toastDescription,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const submitFeedback = async (messageId: string, isPositive: boolean, sessionId?: string) => {
    if (!sessionId) {
      toast({
        title: "Cannot submit feedback",
        description: "Session information not available.",
        variant: "destructive"
      });
      return;
    }

    try {
      const message = messages.find(m => m.id === messageId);
      if (!message) return;

      const { error } = await supabase.from('chat_feedback').insert({
        chat_session_id: sessionId,
        medicine_name: message.medicineName,
        question: messages.find(m => m.type === 'user' && messages.indexOf(m) === messages.indexOf(message) - 1)?.content,
        response: message.content,
        feedback: isPositive
      });

      if (error) {
        throw error;
      }

      // Mark message as feedback given
      setMessages(prev => prev.map(m => 
        m.id === messageId ? { ...m, feedbackGiven: true } : m
      ));

      toast({
        title: "Thank you!",
        description: "Your feedback has been recorded and will help improve our AI assistant."
      });

    } catch (error) {
      console.error('Feedback error:', error);
      toast({
        title: "Error",
        description: "Failed to submit feedback. Please try again.",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="mb-6">
          <h1 className="text-3xl font-bold mb-2">AI Medical Assistant</h1>
          <p className="text-muted-foreground">
            Get answers about medicines and supplements from our AI assistant
          </p>
        </div>


        {/* Chat Container */}
        <Card className="h-[600px] flex flex-col">
          <CardHeader className="border-b flex-shrink-0">
            <div className="flex items-center space-x-2">
              <Stethoscope className="h-5 w-5 text-primary" />
              <CardTitle>AI Medical Assistant</CardTitle>
            </div>
          </CardHeader>

          {/* Messages */}
          <CardContent className="flex-1 p-0 overflow-hidden">
            <ScrollArea className="h-full">
              <div className="p-4 space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${
                      message.type === 'user' ? 'justify-end' : 'justify-start'
                    }`}
                  >
                    <div
                      className={`flex space-x-3 max-w-[80%] ${
                        message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''
                      }`}
                    >
                      <Avatar className="w-8 h-8 flex-shrink-0">
                        <AvatarFallback>
                          {message.type === 'user' ? (
                            <User className="w-4 h-4" />
                          ) : (
                            <Bot className="w-4 h-4" />
                          )}
                        </AvatarFallback>
                      </Avatar>

                      <div
                        className={`rounded-lg p-3 ${
                          message.type === 'user'
                            ? 'bg-primary text-primary-foreground'
                            : 'bg-muted'
                        }`}
                      >
                        <p className="text-sm whitespace-pre-wrap break-words">{message.content}</p>
                        <p className="text-xs opacity-70 mt-1">
                          {message.timestamp.toLocaleTimeString()}
                        </p>

                        {/* Feedback buttons for assistant messages */}
                        {message.type === 'assistant' && message.sessionId && !message.feedbackGiven && (
                          <div className="flex items-center space-x-2 mt-3 pt-2 border-t border-border/20">
                            <span className="text-xs opacity-70">Was this helpful?</span>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => submitFeedback(message.id, true, message.sessionId)}
                              className="h-6 w-6 p-0"
                            >
                              <ThumbsUp className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => submitFeedback(message.id, false, message.sessionId)}
                              className="h-6 w-6 p-0"
                            >
                              <ThumbsDown className="h-3 w-3" />
                            </Button>
                          </div>
                        )}

                        {message.feedbackGiven && (
                          <div className="mt-2 pt-2 border-t border-border/20">
                            <span className="text-xs opacity-70">✓ Feedback recorded</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}

                {isLoading && (
                  <div className="flex justify-start">
                    <div className="flex space-x-3">
                      <Avatar className="w-8 h-8 flex-shrink-0">
                        <AvatarFallback>
                          <Bot className="w-4 h-4" />
                        </AvatarFallback>
                      </Avatar>
                      <div className="bg-muted rounded-lg p-3">
                        <div className="flex items-center space-x-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          <span className="text-sm">Thinking...</span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>
          </CardContent>

          {/* Input Area */}
          <div className="border-t p-4 flex-shrink-0">
            <div className="flex space-x-2">
              <Input
                placeholder="Ask a question about the medicine..."
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                disabled={isLoading}
                className="flex-1"
              />
              <Button
                onClick={sendMessage}
                disabled={isLoading || !inputMessage.trim()}
                size="sm"
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
              </Button>
            </div>

            <div className="flex items-center mt-2 text-xs text-muted-foreground">
              <AlertTriangle className="h-3 w-3 mr-1" />
              For informational purposes only. Always consult healthcare professionals for medical advice.
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default ChatAssistant;
